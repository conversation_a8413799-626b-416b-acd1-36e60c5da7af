#!/usr/bin/env python3
"""
Battery Age Calculator

This script calculates the age of each battery that appears in the working vehicles and HV repair files.
The goal is to find the start date of each battery and calculate its age in days from today.

Data Sources:
- working_vehicles.csv (matching vehicles)
- working_unique_vehicles.csv (unique vehicles)
- hv_repair_2025-06-02b.csv (repair events)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import logging
import sys
from typing import Dict, List, Tuple, Optional, Set, Any, TypedDict
from collections import defaultdict
from sqlalchemy import create_engine, text
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("generate_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryEvent(TypedDict):
    """
    Structure of a battery event as processed from repair data.

    Fields:
    - vin: Vehicle identification number where event occurred
    - date: Date when the event happened (datetime.date object)
    - column: Event type - "old" (removal), "new" (installation), or None (working-only vehicle)
    - event_id: Unique identifier for the repair record (DataFrame index or None for working-only)
    - row_data: Complete repair record data (pandas Series or None for working-only)
    """

    vin: str
    date: datetime.date  # Note: this is datetime.date, not datetime.datetime
    column: Optional[str]  # "old", "new", or None
    event_id: Optional[int]  # DataFrame index or None
    row_data: Optional[Any]  # pandas Series or None


class BatteryInterval(TypedDict):
    """
    Structure of a battery lifecycle interval (one residence period in a vehicle).

    Fields:
    - battery_id: Unique battery identifier
    - vin: Vehicle where battery resided during this interval
    - interval_start: Installation date (datetime.date)
    - interval_end: Removal date (datetime.date or None if still installed)
    - interval_type: Classification - "original", "replacement", "swap", "unknown"
    - source_event_ids: List of repair event IDs that created/closed this interval
    - confidence: Data quality score (0.0-1.0)
    - notes: Additional context or error information
    """

    battery_id: str
    vin: str
    interval_start: Optional[datetime.date]  # None for unknown start
    interval_end: Optional[datetime.date]  # None for currently installed
    interval_type: (
        str  # "based  from start and end, it can be record_start_infer_end,...
    )
    lifecycle_stage: str  # initial, Current, Orphaned, Ghost, Intermediate (Battery installed and later removed)
    source_event_ids: List[Optional[int]]  # Event IDs that created this interval
    confidence: float  # 0.0-1.0 data quality score
    notes: str  # Additional context


class BatteryProcessor:
    """
    Processes lifecycle events for a single battery using state machine approach.

    This class tracks the complete lifecycle of one battery through multiple vehicles.
    It processes chronologically ordered events (installations/removals) and maintains:
    - One open_interval at most (current installation)
    - Multiple completed_intervals (past residence periods)

    State transitions:
    - "new" event: Installation → opens new interval (closes previous if exists)
    - "old" event: Removal → closes current interval
    - None event: Working-only vehicle → creates interval from erstzulassung
    """

    def __init__(self, battery_id: str):
        self.battery_id = battery_id
        self.open_interval = None  # Current active interval (if any)
        self.completed_intervals: List[BatteryInterval] = []  # List of closed intervals
        self.appearance_counter = 0  # Track appearance order for classification


    def process_event(self, event: BatteryEvent) -> None:
        """
        Process a single battery event (installation or removal).

        Args:
            event: BatteryEvent containing vin, date, column, event_id, and row_data
        """
        self.appearance_counter += 1

        if event["column"] == "new":  # Installation event
            self._handle_installation(event)
        elif event["column"] == "old":  # Removal event
            self._handle_removal(event)
        elif event["column"] is None:  # Working-only vehicle
            self._handle_working_only(event)

    def _handle_installation(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Installation event with column="new"
        """
        if self.open_interval:
            # Close previous interval (assume removal before installation)
            self._close_interval(
                event["date"],
                confidence=0.7,
                note="Missing removal - Auto-closed before new installation",
                lifecycle_stage="intermediate",
                interval_type="record_start_infer_end",
            )

        self.open_interval = {
            "vin": event["vin"],
            "start": event["date"],
            "open_src": event.get("event_id"),
            "interval_type": "record_start_ongoing",
            "confidence": 0.8,
            "lifecycle_stage": self._classify_installation_lifecycle_stage(),
            "battery_id": self.battery_id,
        }

    def _handle_removal(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Removal event with column="old"
        """
        if self.open_interval and self.open_interval["vin"] == event["vin"]:
            self._close_interval(
                event["date"],
                confidence=0.95,
                note="",
                lifecycle_stage="intermediate",
                interval_type="record_start_record_end",
            )
        elif self.open_interval:
            # Removal from different vehicle - possible data error
            self._close_interval(
                event["date"],
                confidence=0.6,
                note=f"Removal from {event['vin']} but was installed in {self.open_interval['vin']} on {self.open_interval['start']}",
                lifecycle_stage="intermediate",
                interval_type="record_start_record_end_mismatch",
            )
        else:
            self._handle_orphaned_removal(event)

    def _handle_working_only(self, event: BatteryEvent) -> None:
        """
        Handle battery from working-only vehicle (no repair history).

        Args:
            event: Working-only event with column=None
        """
        if not self.open_interval:
            # Create open interval starting from erstzulassung
            self.open_interval = {
                "vin": event["vin"],
                "start": event["date"],
                "open_src": None,
                "battery_id": self.battery_id,
                "notes": "Working-only vehicle - Start date from erstzulassung",
                "confidence": 0.85,
                "lifecycle_stage": "current",
                "interval_type": "estimate_start_ongoing",
            }

    def _handle_orphaned_removal(self, event: BatteryEvent) -> None:
        """
        Handle removal event without corresponding installation.

        Args:
            event: Orphaned removal event
        """
        # Create and immediately close interval (unknown start date)
        interval = {
            "battery_id": self.battery_id,
            "vin": event["vin"],
            "interval_start": None,
            "interval_end": event["date"],
            "interval_type": "orphaned_removal",
            "source_event_ids": [event.get("event_id")],
            "confidence": 0.3,
            "lifecycle_stage": "orphaned",
            "interval_type": "orphaned_removal",
            "notes": "Removal without installation record",
        }
        self.completed_intervals.append(interval)

    def _close_interval(
        self,
        end_date: datetime,
        confidence: float = 0.95,
        note: str = "",
        lifecycle_stage: str = "",
        interval_type: str = "",
    ) -> None:
        """Close the current open interval."""
        if not self.open_interval:
            return

        interval = {
            "battery_id": self.battery_id,
            "vin": self.open_interval["vin"],
            "interval_start": self.open_interval["start"],
            "interval_end": end_date,
            "lifecycle_stage": lifecycle_stage or self.open_interval["lifecycle_stage"],
            "interval_type": interval_type or self.open_interval["interval_type"],
            "source_event_ids": [self.open_interval["open_src"]],
            "confidence": confidence,
            "notes": note,
        }
        self.completed_intervals.append(interval)
        self.open_interval = None

    def _classify_installation_lifecycle_stage(self) -> str:
        if self.appearance_counter == 1:
            return "initial"
        else:
            return "intermediate"

    def finalize(self) -> List[BatteryInterval]:
        """
        Finalize processing and return all intervals (including open ones).

        Returns:
            List[BatteryInterval]: Complete list of battery residence intervals
        """
        intervals = self.completed_intervals.copy()

        # Add open interval as ongoing
        if self.open_interval:
            interval: BatteryInterval = {
                "battery_id": self.battery_id,
                "vin": self.open_interval["vin"],
                "interval_start": self.open_interval["start"],
                "interval_end": None,  # Still active
                "interval_type": "record_start_ongoing",
                "lifecycle_stage": "current",
                "source_event_ids": [self.open_interval["open_src"]],
                "confidence": 0.95,
                "notes": "",
            }
            intervals.append(interval)

        return intervals


class BatteryAgeCalculator:
    """Calculate battery ages based on repair events and vehicle data."""

    def __init__(self):
        self.today = datetime.now().date()

        self.db_engine = None

        # Data containers
        self.hv_repair_df = None
        self.daily_stats_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None

        # Processing results
        self.daily_stats_by_vehicle = {}  # Pre-indexed by vehicle_id for fast lookup
        self.battery_vehicles = {}  # battery_id -> list of battery appearance dicts
        self.vehicle_info = {}  # vin -> vehicle info
        self.vin_to_vehicle_id = {}
        self.unique_vehicles = set()
        self.unique_batteries = set()
        self.vin_without_vehicle_id = set()

        # Enhanced lifecycle tracking
        self.battery_processors = {}  # battery_id -> BatteryProcessor
        self.battery_timelines = []  # Complete interval records
        self.raw_battery_timelines = []  # Complete interval records before vin stiching

        # ─── conflict bookkeeping ──────────────────────────────
        self.conflicts = []
        self.phantom_battery_timelines = []

        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def _initialize_database_connection(self):
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            self.db_engine = create_engine(db_connection_string)
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _load_vin_mappings(self):
        """Load VIN to vehicle_id mapping from database"""
        if not self.db_engine:
            logger.warning(
                "No database connection - cant not load VIN to vehicle_id mapping"
            )
            raise

        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )

            # Build VIN to vehicle_id mapping
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        self._initialize_database_connection()

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load working vehicles data
        logger.info("Loading working vehicles data and daily stats...")
        self.working_vehicles_df = pd.read_csv(
            "comparison_results/working_matching_vehicles.csv"
        )
        self.working_unique_df = pd.read_csv(
            "comparison_results/working_unique_vehicles.csv"
        )
        self.daily_stats_df = pd.read_csv(
            "daily_stats.csv",
            dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
            parse_dates=["date"],
        )

        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")
        logger.info(f"Loaded {len(self.daily_stats_df)} daily stats records")

        # Combine working vehicles
        self.working_vehicles_df = pd.concat(
            [self.working_vehicles_df, self.working_unique_df], ignore_index=True
        )
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")

        logger.info("Loading VIN to vehicle_id mapping from database...")
        self._load_vin_mappings()

        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
        for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
            # Sort by date for each vehicle
            vehicle_data = group.sort_values("date").copy()
            self.daily_stats_by_vehicle[vehicle_id] = vehicle_data
        logger.info(
            f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
        )
        logger.info(
            "Memory optimization: clear dailystats dataframe as we now have indexed data"
        )
        self.daily_stats_df = None

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date (battery_changed if available, otherwise created)
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Clean working vehicles data
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Filter valid records
        self.hv_repair_df = self.hv_repair_df.dropna(subset=["vin", "effective_date"])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=["vin"])

        logger.info(
            f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles"
        )

    def process_hv_repair_data(self):
        """Process HV repair data to track battery appearances."""
        logger.info("Processing HV repair data...")

        # Sort by effective date to process chronologically. First event = first appearance = earliest seen date of batteries (old or new)
        self.hv_repair_df = self.hv_repair_df.sort_values("effective_date").reset_index(
            drop=True
        )

        for idx, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx  # Use DataFrame index as event ID
            # Process old battery appearance
            if old_battery and pd.notna(old_battery):
                self.unique_batteries.add(old_battery)
                if old_battery not in self.battery_vehicles:
                    self.battery_vehicles[old_battery] = []
                self.battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )

            # Process new battery appearance
            if new_battery and pd.notna(new_battery):
                self.unique_batteries.add(new_battery)
                if new_battery not in self.battery_vehicles:
                    self.battery_vehicles[new_battery] = []
                self.battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )

            self.unique_vehicles.add(vin)

        logger.info(f"Found {len(self.unique_batteries)} batteries from repair data")

    def _get_first_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def _get_last_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def build_vehicle_info(self):
        logger.info("Building vehicle information...")

        for _, row in self.working_vehicles_df.iterrows():
            vin = row["vin"]
            self.unique_vehicles.add(vin)
            first_active_date = self._get_first_active_date_for_vin(vin)
            self.vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "first_active_date": first_active_date,
                "last_active_date": self._get_last_active_date_for_vin(vin),
            }
        # We also need to get vehicle info for vehicles that appear in repair data but not in working data
        for _, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            if vin not in self.vehicle_info:
                first_active_date = self._get_first_active_date_for_vin(vin)
                self.vehicle_info[vin] = {
                    "vin": vin,
                    "erstzulassung": first_active_date,
                    "first_active_date": first_active_date,
                    "master_battery": None,
                    "slave_battery": None,
                    "akz": None,
                    "last_active_date": self._get_last_active_date_for_vin(vin),
                }

        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")

    def add_vehicles_from_working_only_data(self):
        logger.info("Adding batteries from working-only vehicles...")

        repair_vins = set(self.hv_repair_df["vin"].unique())
        working_vins = set(self.vehicle_info.keys())

        # Add batteries from vehicles that only appear in working data
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._add_working_only_batteries(vin)

        self.stats["working_only_vehicles"] = len(working_only_vins)
        logger.info(
            f"Added batteries from {len(working_only_vins)} working-only vehicles"
        )

    def _add_working_only_batteries(self, vin: str):
        """Add batteries from vehicles that only appear in working data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info["erstzulassung"]

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        # Add batteries from working data
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.unique_batteries.add(battery_id)
                if battery_id not in self.battery_vehicles:
                    self.battery_vehicles[battery_id] = []
                
                # Check for duplicates - don't add if battery already has events for this VIN
                if not any(e["vin"] == vin for e in self.battery_vehicles[battery_id]):
                    self.battery_vehicles[battery_id].append(
                        {
                            "vin": vin,
                            "date": erstzulassung,
                            "column": None,
                            "event_id": None,
                            "row_data": None,
                        }
                    )
                    logger.debug(f"Added working-only battery {battery_id} for VIN {vin}")
                else:
                    logger.debug(f"Skipped duplicate: Battery {battery_id} already has repair events for VIN {vin}")

    def build_battery_timelines(self):
        logger.info("Building battery lifecycle timelines...")

        # Process each battery independently
        for battery_id in self.unique_batteries:
            if (
                battery_id not in self.battery_vehicles
                or not self.battery_vehicles[battery_id]
            ):
                logger.warning(f"Battery {battery_id} has no events")
                continue

            # Create processor for this battery
            processor = BatteryProcessor(battery_id)
            self.battery_processors[battery_id] = processor

            # Sort events chronologically with deterministic tie-breaking

            events = sorted(
                self.battery_vehicles[battery_id],
                key=lambda x: (
                    x["date"],
                    0 if x["column"] == "old" else 1,  # tie-breaker
                    x.get("event_id", 0),  # stable deterministic order
                ),
            )
            # Process events through state machine
            for event in events:
                processor.process_event(event)

            # Finalize and collect intervals
            intervals = processor.finalize()
            self.battery_timelines.extend(intervals)

        logger.info(
            f"Built timelines with {len(self.battery_timelines)} intervals for {len(self.battery_processors)} batteries"
        )

    def _vehicle_was_active(
        self, vin, start_date, end_date, daily_stats_by_vehicle, vin_to_vehicle_id
    ):
        """Check if vehicle drove during the specified date range."""
        vehicle_id = vin_to_vehicle_id.get(vin)
        if vehicle_id not in daily_stats_by_vehicle:
            return False

        stats = daily_stats_by_vehicle[vehicle_id]

        # Convert date objects to pandas Timestamps for comparison
        start_ts = pd.Timestamp(start_date) if start_date else None
        end_ts = pd.Timestamp(end_date) if end_date else None

        if start_ts is None or end_ts is None:
            return False

        window = stats[(stats["date"] >= start_ts) & (stats["date"] <= end_ts)]

        if window.empty:
            return False

        valid_records = window[
            (window["km_start"] >= 0)
            & (window["km_end"] >= 0)
            & (window["km_end"] >= window["km_start"])
        ]

        if valid_records.empty:
            return False

        km_driven = (
            valid_records["km_end"]
            .subtract(valid_records["km_start"], fill_value=0)
            .sum()
        )

        return km_driven > 1

    def stitch_vehicle_timelines(
        self,
        timelines: List[BatteryInterval],
        vehicle_info: Dict[str, Any],
        daily_stats_by_vehicle: Dict[int, pd.DataFrame],
        vin_to_vehicle_id,
        conflicts,
        max_gap_days=30,
    ) -> List[BatteryInterval]:
        """
        Activity-aware timeline stitching per VIN.

        Args:
            timelines: List of BatteryInterval dicts from BatteryProcessor
            vehicle_info: Dict[vin] -> {erstzulassung, first_active_date, ...}
            daily_stats_by_vehicle: Dict[vehicle_id] -> DataFrame with daily stats
            vin_to_vehicle_id: Dict[vin] -> vehicle_id mapping
            max_gap_days: Max days to auto-fill without activity check

        Returns:
            List of stitched BatteryInterval dicts
        """

        usage_by_batt: Dict[str, List[BatteryInterval]] = defaultdict(list)
        by_vin = defaultdict(list)
        for iv in timelines:
            usage_by_batt[iv["battery_id"]].append(iv)
            by_vin[iv["vin"]].append(iv)

        def _battery_used_elsewhere(
            batt_id, a, b, this_vin, conflict_type, candidate_source_event_ids=None,debug_note=""
        ):
            conflict_count = 0
            if batt_id == "V6P0116S000AA04465":
                logger.info(f"Debugging V6P0116S000AA04465: {usage_by_batt.get(batt_id, [])}")
            for iv in usage_by_batt.get(batt_id, []):
                if iv["vin"] == this_vin:
                    continue
                s = iv["interval_start"] or date.min
                e = iv["interval_end"] or date.max
                if s < b and a < e:
                    conflict_count += 1
                    conflict = {
                        "type": conflict_type,
                        "battery_id": batt_id,
                        "candidate_vin": this_vin,
                        "other_vin": iv["vin"],
                        "candidate_vin_start_day": a,
                        "candidate_vin_end_day": b,
                        "other_vin_start_date": s,
                        "other_vin_end_date": e,
                        "confidence": iv["confidence"],
                        "candidate_source_event_ids": candidate_source_event_ids,
                        "debug_note": debug_note,
                    }
                    conflicts.append(conflict)
                    logger.info(conflict)

            # battery can only be in one place at a time
            return conflict_count >= 1

        stitched = []

        for vin, intervals in by_vin.items():
            if not intervals:
                continue

            # 1. Sort chronologically
            intervals.sort(
                key=lambda iv: (
                    iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                    iv["interval_end"] or date(9999, 12, 31),
                )
            )

            # ----------------
            # 2. Middle gaps and orphaned removal fixes
            # ----------------
            filled = []
            if len(intervals) >= 2:
                for prev, nxt in zip(intervals, intervals[1:]):
                    filled.append(prev)

                    # if nxt == intervals[-1]:
                    #     continue

                    if (
                        nxt["interval_start"] is None
                        and prev["interval_end"] is not None
                    ):

                        # Calculate gap duration, if there is no end date, assume 1 year
                        gap_end_date = nxt["interval_end"] or prev[
                            "interval_end"
                        ] + timedelta(days=365)
                        gap_days = (gap_end_date - prev["interval_end"]).days

                        # Check activity during gap
                        activity_window_end = min(
                            gap_end_date, prev["interval_end"] + timedelta(days=365)
                        )
                        was_active = self._vehicle_was_active(
                            vin,
                            prev["interval_end"],
                            activity_window_end,
                            daily_stats_by_vehicle,
                            vin_to_vehicle_id,
                        )
                        if was_active or gap_days <= max_gap_days:
                            # Vehicle was active, that means a battery must have been installed
                            if _battery_used_elsewhere(
                                nxt["battery_id"],
                                prev["interval_end"],
                                gap_end_date,
                                vin,
                                "gap_filling",
                                nxt["source_event_ids"],
                            ):
                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    (nxt.get("notes") or "")
                                    + " | start imputed from previous removal, but battery used elsewhere"
                                )
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["lifecycle_stage"] = nxt.get(
                                    "lifecycle_stage", "intermediate"
                                )
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.5)
                            else:
                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    nxt.get("notes") or ""
                                ) + " | start imputed from previous removal"
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["lifecycle_stage"] = nxt.get(
                                    "lifecycle_stage", "intermediate"
                                )
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.6)
                        else:
                            # Vehicle was inactive
                            if _battery_used_elsewhere(
                                nxt["battery_id"],
                                prev["interval_end"],
                                gap_end_date,
                                vin,
                                "gap_filling",
                                nxt["source_event_ids"],
                            ):
                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    (nxt.get("notes") or "")
                                    + " | start imputed from previous removal, vehicle inactive, battery used elsewhere"
                                )
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["lifecycle_stage"] = nxt.get(
                                    "lifecycle_stage", "intermediate"
                                )
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.2)
                            else:
                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    (nxt.get("notes") or "")
                                    + " | start imputed from previous removal, vehicle inactive"
                                )
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["lifecycle_stage"] = nxt.get(
                                    "lifecycle_stage", "intermediate"
                                )
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.3)

                    elif nxt["interval_start"] is None and prev["interval_end"] is None:
                        # Dual-battery setup: both batteries active simultaneously
                        # Keep them as concurrent intervals rather than overlapping
                        nxt["interval_start"] = prev["interval_start"]
                        nxt["notes"] = "Concurrent battery in dual-battery vehicle"
                        nxt["interval_type"] = "infer_start_record_end"
                        nxt["lifecycle_stage"] = "intermediate"
                        nxt["confidence"] = 0.6
                        prev["lifecycle_stage"] = "current"
                        prev["confidence"] = 0.6

                    # Handle small gaps between known intervals
                    elif (
                        prev["interval_end"]
                        and nxt["interval_start"]
                        and (nxt["interval_start"] - prev["interval_end"]).days > 1
                    ):
                        # Probably maintaince event
                        pass
                        # gap_days = (nxt["interval_start"] - prev["interval_end"]).days
                        # was_active = self._vehicle_was_active(
                        #     vin,
                        #     prev["interval_end"],
                        #     nxt["interval_start"],
                        #     daily_stats_by_vehicle,
                        #     vin_to_vehicle_id,
                        # )

                        # if gap_days > max_gap_days:
                        #     if was_active:
                        #         filled.append(
                        #             {
                        #                 "battery_id": f"MAINT_{vin}_{prev['interval_end']}",
                        #                 "vin": vin,
                        #                 "interval_start": prev["interval_end"],
                        #                 "interval_end": nxt["interval_start"],
                        #                 "interval_type": "infer_start_infer_end",
                        #                 "lifecycle_stage": "intermediate",
                        #                 "source_event_ids": [],
                        #                 "confidence": 0.3,
                        #                 "notes": "vehicle active - maintenance gap",
                        #             }
                        #         )
                        #     else:
                        #         filled.append(
                        #             {
                        #                 "battery_id": f"MAINT_{vin}_{prev['interval_end']}",
                        #                 "vin": vin,
                        #                 "interval_start": prev["interval_end"],
                        #                 "interval_end": nxt["interval_start"],
                        #                 "interval_type": "infer_start_infer_end",
                        #                 "lifecycle_stage": "intermediate",
                        #                 "source_event_ids": [],
                        #                 "confidence": 0.2,
                        #                 "notes": "vehicle inactive - maintenance gap",
                        #             }
                        #         )

                # Add final interval
                filled.append(intervals[-1])
            else:
                filled = intervals.copy()

            intervals = filled

            # ----------------
            # 3. Erstzulassung extension (vehicle can not drive without a battery)
            # ----------------
            if intervals and intervals[0]["interval_start"] is None:
                vehicle_data = vehicle_info.get(vin, {})
                start_date = vehicle_data.get("erstzulassung") or vehicle_data.get("first_active_date")
                
                if start_date and intervals[0]["interval_end"]:  # Only if we have a real end date
                    if not _battery_used_elsewhere(
                        intervals[0]["battery_id"],
                        start_date,
                        intervals[0]["interval_end"], 
                        vin,
                        "erstzulassung_extension",
                        intervals[0]["source_event_ids"],
                        "cac"
                    ):
                        intervals[0]["interval_start"] = start_date
                        intervals[0]["notes"] = (intervals[0].get("notes") or "") + " | start imputed from erstzulassung"
                        intervals[0]["confidence"] = 0.75
                        intervals[0]["interval_type"] = "infer_start_record_end"
                        intervals[0]["lifecycle_stage"] = "initial"
                else:
                    logger.warning(f"Vehicle {vin} has no erstzulassung date")
                    intervals[0]["interval_start"] = intervals[0]["interval_end"]
                    intervals[0]["confidence"] = 0
                    intervals[0]["notes"] = (
                        intervals[0].get("notes") or ""
                    ) + " | no erstzulassung or first active date"
                    intervals[0]["interval_type"] = "infer_start_record_end"
                    intervals[0]["lifecycle_stage"] = "initial"

            elif intervals and intervals[0]["interval_start"] is not None:
                # Check if first interval starts after vehicle's first activity
                vehicle_data = vehicle_info.get(vin, {})
                erstzulassung = vehicle_data.get("erstzulassung") or vehicle_data.get(
                    "first_active_date"
                )

                if erstzulassung and intervals[0]["interval_start"] > erstzulassung:
                    # Gap between erstzulassung and first recorded battery
                    if not _battery_used_elsewhere(
                        intervals[0]["battery_id"],
                        erstzulassung,
                        intervals[0]["interval_start"],
                        vin,
                        "erstzulassung_extension",
                        intervals[0]["source_event_ids"],
                        "loz"
                    ):
                        interval = {
                            "battery_id": intervals[0]["battery_id"],
                            "vin": vin,
                            "interval_start": erstzulassung,
                            "interval_end": intervals[0]["interval_start"],
                            "interval_type": "infer_start_infer_end",
                            "lifecycle_stage": "initial",
                            "source_event_ids": intervals[0]["source_event_ids"],
                            "confidence": 0.7,
                            "notes": "Imputed interval - vehicle active before first recorded battery battery not used elsewhere",
                        }
                        intervals.insert(0, interval)
                    else:
                        # Insert a battery with no known start (this will be resolved in next stage)
                        interval = {
                            "battery_id": intervals[0]["battery_id"],
                            "vin": vin,
                            "interval_start": None,
                            "interval_end": intervals[0]["interval_start"],
                            "interval_type": "infer_start_infer_end",
                            "lifecycle_stage": "initial",
                            "source_event_ids": intervals[0]["source_event_ids"],
                            "confidence": 0.4,
                            "notes": "Imputed interval - vehicle active before first recorded battery, but recorded battery used elsewhere",
                        }
                        intervals.insert(0, interval)

            # ----------------
            # 3. Post-activity extension (Last interval is CLOSED, but daily-stats show km afterwards.)
            # ----------------
            last_iv = intervals[-1] if intervals else None

            if last_iv and last_iv["interval_end"] is not None:
                was_active_after = self._vehicle_was_active(
                    vin,
                    last_iv["interval_end"] + timedelta(days=90),
                    self.today,
                    daily_stats_by_vehicle,
                    vin_to_vehicle_id,
                )

                if was_active_after:
                    if not _battery_used_elsewhere(
                        last_iv["battery_id"],
                        last_iv["interval_end"],
                        self.today,
                        vin,
                        "post_activity_extension",
                        last_iv["source_event_ids"],
                    ):
                        new_iv = {
                            "battery_id": last_iv["battery_id"],
                            "vin": vin,
                            "interval_start": last_iv["interval_end"],
                            "interval_end": None,  # still installed
                            "interval_type": "infer_start_infer_end",
                            "lifecycle_stage": "current",
                            "source_event_ids": last_iv["source_event_ids"],
                            "confidence": 0.75,
                            "notes": (
                                "Imputed interval - vehicle active after last removal, battery assumed to be still inside"
                            ),
                        }
                        intervals.append(new_iv)
                        usage_by_batt[last_iv["battery_id"]].append(new_iv)
                    else:
                        new_iv = {
                            "battery_id": last_iv["battery_id"],
                            "vin": vin,
                            "interval_start": last_iv["interval_end"],
                            "interval_end": None,  # still installed
                            "interval_type": "infer_start_infer_end",
                            "lifecycle_stage": "current",
                            "source_event_ids": last_iv["source_event_ids"],
                            "confidence": 0.3,
                            "notes": (
                                "Imputed interval - vehicle active after last removal, battery assumed to be still inside, but also installed elsewhere"
                            ),
                        }
                        intervals.append(new_iv)
                        usage_by_batt[last_iv["battery_id"]].append(new_iv)
            stitched.extend(intervals)
        return stitched, conflicts

    def resolve_battery_conflicts(
        self,
        stitched_timelines: List[BatteryInterval],
        conflicts,
    ):
        if not conflicts:
            logger.info("No conflicts to resolve")
            return stitched_timelines

        # Build O(1) lookup index: (battery_id, vin) -> list of intervals
        timeline_index = defaultdict(list)
        for iv in stitched_timelines:
            key = (iv["battery_id"], iv["vin"])
            timeline_index[key].append(iv)

        # Build usage_by_batt index for conflict resolution
        usage_by_batt = defaultdict(list)
        for iv in stitched_timelines:
            usage_by_batt[iv["battery_id"]].append(iv)

        erstzulassung_conflicts_by_batt = defaultdict(list)
        post_activity_conflicts_by_batt = defaultdict(list)
        for conflict in conflicts:
            if conflict["type"] == "erstzulassung_extension":
                bat = conflict["battery_id"]
                erstzulassung_conflicts_by_batt[bat].append(conflict)
            if conflict["type"] == "post_activity_extension":
                bat = conflict["battery_id"]
                post_activity_conflicts_by_batt[bat].append(conflict)

        for bat, conflicts in erstzulassung_conflicts_by_batt.items():
            logger.info(f"Resolving erstzulassung conflicts for battery {bat}")

            # Sort conflicts by earliest start date and source event IDs
            conflicts.sort(
                key=lambda x: (
                    x["candidate_vin_start_day"],
                    (
                        min(x["candidate_source_event_ids"])
                        if x["candidate_source_event_ids"]
                        else float("inf")
                    ),
                    x["candidate_vin_end_day"],
                )
            )

            # Use sweep line algorithm to sequence intervals
            intervals_to_resolve = []
            for conflict in conflicts:
                lookup_key = (bat, conflict["candidate_vin"])
                if lookup_key in timeline_index:
                    for iv in timeline_index[lookup_key]:
                        if (
                            iv["interval_start"] is None
                            and iv["interval_end"] == conflict["candidate_vin_end_day"]
                        ):
                            intervals_to_resolve.append({
                                "interval": iv,
                                "conflict": conflict,
                                "desired_start": conflict["candidate_vin_start_day"]
                            })
                            break

            if not intervals_to_resolve:
                continue

            # Sort intervals by desired start date
            intervals_to_resolve.sort(key=lambda x: x["desired_start"])

            # Sequential assignment with sweep line logic
            current_start_date = intervals_to_resolve[0]["desired_start"]
            
            for i, item in enumerate(intervals_to_resolve):
                iv = item["interval"]
                conflict = item["conflict"]
                
                # Ensure start <= end
                if current_start_date <= iv["interval_end"]:
                    iv["interval_start"] = current_start_date
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start imputed from sequential conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = max(0.6 - (i * 0.1), 0.3)  # Decrease confidence for later assignments
                    iv["lifecycle_stage"] = "initial"
                    iv["interval_type"] = "infer_start_record_end"
                    
                    logger.info(
                        f"  → RESOLVED: Set {conflict['candidate_vin']} start to {current_start_date} (position {i})"
                    )

                    # Update usage_by_batt to reflect the change
                    for usage_iv in usage_by_batt[bat]:
                        if (
                            usage_iv["vin"] == iv["vin"]
                            and usage_iv["interval_end"] == iv["interval_end"]
                        ):
                            usage_iv["interval_start"] = current_start_date
                            break

                    # Next interval starts when this one ends
                    current_start_date = iv["interval_end"]
                else:
                    # start > end -> set start to end (zero duration)
                    iv["interval_start"] = iv["interval_end"]
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start set to end due to conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = 0.2
                    iv["lifecycle_stage"] = "initial"
                    iv["interval_type"] = "infer_start_record_end"
                    
                    logger.info(
                        f"  → RESOLVED (zero duration): Set {conflict['candidate_vin']} start=end={iv['interval_end']} (position {i})"
                    )
        return stitched_timelines

    def limit_concurrent_batteries_per_vin(self, stitched_timelines: List[BatteryInterval]) -> List[BatteryInterval]:
        """
        Ensure each VIN has at most 2 active batteries at any point in time.
        Uses sweep line algorithm to detect and resolve historical overlaps.
        """
        logger.info("Limiting concurrent batteries per VIN to maximum 2 (historical + current)...")
        
        # Group by VIN
        by_vin = defaultdict(list)
        for iv in stitched_timelines:
            by_vin[iv["vin"]].append(iv)
        
        for vin, intervals in by_vin.items():
            if len(intervals) <= 2:
                continue  # No possible overlaps
            
            # Build events list: (date, event_type, interval_obj)
            events = []
            for iv in intervals:
                start_date = iv["interval_start"] or date.min
                end_date = iv["interval_end"] or date.max
                
                events.append((start_date, "start", iv))
                if iv["interval_end"] is not None:  # Don't add end event for open intervals
                    events.append((end_date, "end", iv))
            
            # Sort by date, with "end" events before "start" events on same date
            events.sort(key=lambda x: (x[0], x[1] == "start"))
            
            # Sweep line algorithm
            active_intervals = []
            
            for event_date, event_type, interval_obj in events:
                if event_type == "start":
                    active_intervals.append(interval_obj)
                    
                    # Check if we exceed 2 concurrent batteries
                    if len(active_intervals) > 2:
                        logger.warning(f"VIN {vin} has {len(active_intervals)} concurrent batteries on {event_date}")
                        
                        # Sort by confidence (lowest first) and close the worst one
                        active_intervals.sort(key=lambda x: (
                            x.get("confidence", 0),
                            max(x.get("source_event_ids", [0]) or [0])  # Lower max IDs come first
                        ))
                        
                        # Close the lowest confidence interval
                        interval_to_close = active_intervals.pop(0)
                        interval_to_close["interval_end"] = event_date
                        interval_to_close["notes"] = (
                            interval_to_close.get("notes", "") + 
                            f" | Closed on {event_date} due to >2 concurrent batteries limit"
                        )
                        interval_to_close["confidence"] = min(interval_to_close.get("confidence", 1.0), 0.3)
                        interval_to_close["lifecycle_stage"] = "intermediate"
                        
                        logger.info(f"  → Closed battery {interval_to_close['battery_id']} on {event_date}")
                        logger.info(" ")
                    
                elif event_type == "end":
                    # Remove from active set
                    active_intervals = [iv for iv in active_intervals if iv != interval_obj]
        
        return stitched_timelines

    def generate_outputs(self):
        """Generate output files."""
        logger.info("Generating output files...")

        # Generate enhanced timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate statistics file
        stats_filename = f"battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {len(self.unique_batteries)}\n")
            f.write(f"Total Vehicles: {len(self.unique_vehicles)}\n")
            f.write(f"Working-only Vehicles: {self.stats['working_only_vehicles']}\n")
            f.write(f"Errors: {len(self.stats['errors'])}\n")

            # Enhanced statistics
            if hasattr(self, "battery_timelines"):
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")
                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            if self.stats["errors"]:
                f.write("\nErrors:\n")
                for error in self.stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        # Return the timeline CSV if it exists, otherwise return None
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            return timeline_csv, stats_filename
        else:
            return None, stats_filename

    def generate_timeline_outputs(self):
        """Generate timeline output files only."""
        logger.info("Generating timeline output files...")

        # Generate timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

            # Generate timeline statistics
            stats_filename = f"battery_timeline_statistics.txt"
            with open(stats_filename, "w") as f:
                f.write("Battery Timeline Analysis Statistics\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Processing Date: {datetime.now()}\n")
                f.write(f"Total Batteries: {len(self.battery_processors)}\n")
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")

                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

                # Confidence distribution
                confidences = [i["confidence"] for i in self.battery_timelines]
                avg_confidence = (
                    sum(confidences) / len(confidences) if confidences else 0
                )
                f.write(f"Average Confidence: {avg_confidence:.3f}\n")

                # Interval type distribution
                interval_types = {}
                for interval in self.battery_timelines:
                    interval_type = interval["interval_type"]
                    interval_types[interval_type] = (
                        interval_types.get(interval_type, 0) + 1
                    )

                f.write(f"\nInterval Type Distribution:\n")
                for interval_type, count in interval_types.items():
                    f.write(f"  {interval_type}: {count}\n")

            logger.info(f"Saved timeline statistics to {stats_filename}")
            return timeline_csv
        else:
            logger.warning("No timeline data to output")
            return None

    def run(self):
        """Run the battery timeline analysis process."""
        logger.info("Starting battery timeline analysis...")

        try:
            self.load_data()
            self.clean_data()
            self.process_hv_repair_data()
            self.build_vehicle_info()
            self.add_vehicles_from_working_only_data()

            # Set basic stats for timeline analysis
            self.stats["total_batteries"] = len(self.unique_batteries)
            self.stats["total_vehicles"] = len(self.unique_vehicles)

            logger.info("Running timeline analysis...")
            self.build_battery_timelines()

            logger.info("Battery Timeline completed! Proceed to vin stiching...")
            self.raw_battery_timelines = self.battery_timelines.copy()
            self.battery_timelines, self.conflicts = self.stitch_vehicle_timelines(
                self.battery_timelines,
                self.vehicle_info,
                self.daily_stats_by_vehicle,
                self.vin_to_vehicle_id,
                self.conflicts,
            )
            logger.info(
                "Battery Timeline completed! Proceed to resolve battery conflicts..."
            )
            self.battery_timelines = self.resolve_battery_conflicts(
                self.battery_timelines,
                self.conflicts,
            )
            self.battery_timelines = self.limit_concurrent_batteries_per_vin(
                self.battery_timelines
            )
            csv_file, stats_file = self.generate_outputs()

            logger.info("Battery timeline analysis completed successfully!")
            if csv_file:
                logger.info(f"Timeline data saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            logger.info("Enhanced timeline analysis completed!")
            logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
            logger.info(f"Batteries with timelines: {len(self.battery_processors)}")

            # Generate dual-battery report
            dual_battery_report = self.generate_dual_battery_report()
            logger.info(f"Dual-battery vehicles found: {dual_battery_report['total_dual_battery_vehicles']}")

            # Optionally save to stats file
            with open(stats_file, "a") as f:
                f.write(f"\nDual-Battery Configuration:\n")
                f.write(f"  Vehicles with 2 active batteries: {dual_battery_report['total_dual_battery_vehicles']}\n")
                f.write(f"  VINs: {', '.join(dual_battery_report['dual_battery_vins'][:10])}{'...' if len(dual_battery_report['dual_battery_vins']) > 10 else ''}\n")

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise

    def generate_dual_battery_report(self):
        """Generate report on dual-battery configurations"""
        dual_battery_vins = []
        
        # Group intervals by VIN
        by_vin = defaultdict(list)
        for iv in self.battery_timelines:
            by_vin[iv["vin"]].append(iv)
        
        for vin, intervals in by_vin.items():
            # Count currently active batteries (interval_end is None)
            concurrent = len([iv for iv in intervals if iv["interval_end"] is None])
            if concurrent == 2:
                dual_battery_vins.append(vin)
        
        return {
            "total_dual_battery_vehicles": len(dual_battery_vins),
            "dual_battery_vins": dual_battery_vins
        }


if __name__ == "__main__":
    import sys

    # Run timeline analysis only (age calculations are commented out)
    logger.info("Running battery timeline analysis...")
    calculator = BatteryAgeCalculator()
    calculator.run()
